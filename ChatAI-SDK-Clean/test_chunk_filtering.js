#!/usr/bin/env node

/**
 * Test script for chunk filtering functionality
 * Demonstrates how junk chunks are identified and filtered out
 * Run with: node test_chunk_filtering.js
 */

// Import the filterJunkChunks function from vectorProcessing
// Note: In a real scenario, you'd extract this to a separate utility module
function filterJunkChunks(chunks, chunkMetadata) {
  // Configurable filtering criteria
  const filterCriteria = {
    minChars: 100,        // Minimum character count
    minWords: 15,         // Minimum word count
    minMeaningfulRatio: 0.3, // Minimum ratio of meaningful content
    maxRepetitionRatio: 0.7, // Maximum ratio of repeated content
  };

  const filteredChunks = [];
  const filteredMetadata = [];
  const filterReport = {
    filtered: [],
    reasons: {
      tooShort: 0,
      lowWordCount: 0,
      lowMeaningful: 0,
      highRepetition: 0,
      emptyContent: 0
    }
  };

  chunks.forEach((chunk, index) => {
    const metadata = chunkMetadata[index];
    const chunkId = metadata.pageNumber ? `Page ${metadata.pageNumber}` : `Chunk ${index + 1}`;
    
    // Calculate chunk statistics
    const charCount = chunk.length;
    const wordCount = chunk.trim().split(/\s+/).filter(word => word.length > 0).length;
    const meaningfulContent = chunk.replace(/[\s\n\r\t]+/g, ' ').trim();
    
    // Check for empty or whitespace-only content
    if (!meaningfulContent || meaningfulContent.length < 10) {
      filterReport.filtered.push({
        identifier: chunkId,
        reason: 'Empty or whitespace-only content',
        chars: charCount,
        words: wordCount
      });
      filterReport.reasons.emptyContent++;
      return;
    }

    // Check minimum character count
    if (charCount < filterCriteria.minChars) {
      filterReport.filtered.push({
        identifier: chunkId,
        reason: `Too short (${charCount} < ${filterCriteria.minChars} chars)`,
        chars: charCount,
        words: wordCount
      });
      filterReport.reasons.tooShort++;
      return;
    }

    // Check minimum word count
    if (wordCount < filterCriteria.minWords) {
      filterReport.filtered.push({
        identifier: chunkId,
        reason: `Low word count (${wordCount} < ${filterCriteria.minWords} words)`,
        chars: charCount,
        words: wordCount
      });
      filterReport.reasons.lowWordCount++;
      return;
    }

    // Check for meaningful content ratio (letters/numbers vs total)
    const meaningfulChars = (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length;
    const meaningfulRatio = meaningfulChars / meaningfulContent.length;
    
    if (meaningfulRatio < filterCriteria.minMeaningfulRatio) {
      filterReport.filtered.push({
        identifier: chunkId,
        reason: `Low meaningful content ratio (${Math.round(meaningfulRatio * 100)}% < ${Math.round(filterCriteria.minMeaningfulRatio * 100)}%)`,
        chars: charCount,
        words: wordCount
      });
      filterReport.reasons.lowMeaningful++;
      return;
    }

    // Check for high repetition (common in headers, footers, page numbers)
    const words = meaningfulContent.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = 1 - (uniqueWords.size / words.length);
    
    if (repetitionRatio > filterCriteria.maxRepetitionRatio && words.length > 5) {
      filterReport.filtered.push({
        identifier: chunkId,
        reason: `High repetition ratio (${Math.round(repetitionRatio * 100)}% > ${Math.round(filterCriteria.maxRepetitionRatio * 100)}%)`,
        chars: charCount,
        words: wordCount
      });
      filterReport.reasons.highRepetition++;
      return;
    }

    // Chunk passed all filters - keep it
    filteredChunks.push(chunk);
    
    // Update metadata with original index for tracking
    const updatedMetadata = {
      ...metadata,
      originalIndex: index,
      filteredIndex: filteredChunks.length - 1,
      qualityScore: {
        charCount,
        wordCount,
        meaningfulRatio: Math.round(meaningfulRatio * 100),
        repetitionRatio: Math.round(repetitionRatio * 100)
      }
    };
    filteredMetadata.push(updatedMetadata);
  });

  return {
    filteredChunks,
    filteredMetadata,
    filterReport
  };
}

async function testChunkFiltering() {
  console.log('🧪 Testing Chunk Filtering Functionality');
  console.log('========================================\n');

  // Test data - mix of good and junk chunks
  const testChunks = [
    // Good chunks (should pass filtering)
    'This is a comprehensive paragraph about machine learning algorithms and their applications in natural language processing. It contains substantial content with meaningful information that would be valuable for semantic search and retrieval.',
    
    'The document processing pipeline involves several stages including parsing, chunking, embedding generation, and vector storage. Each stage has its own optimization opportunities and performance considerations that need to be carefully balanced.',
    
    // Junk chunks (should be filtered out)
    'Page 1',  // Too short
    '   \n\n\t   ',  // Whitespace only
    'Header Header Header Header Header',  // High repetition
    'Page 7 of 100',  // Too short
    '© 2024 Company Name. All rights reserved.',  // Short copyright notice
    'Table of Contents',  // Too short
    '..........................................',  // Low meaningful content
    'Chapter Chapter Chapter Chapter Chapter Chapter',  // High repetition
    
    // More good chunks
    'Vector databases like Qdrant provide efficient similarity search capabilities for large-scale document retrieval systems. They use advanced indexing techniques to enable fast approximate nearest neighbor searches across high-dimensional embedding spaces.',
    
    'The optimization of batch processing involves careful consideration of rate limits, network overhead, and memory usage. By processing multiple items together, we can significantly reduce the total time required for large-scale operations.'
  ];

  const testMetadata = testChunks.map((chunk, index) => ({
    chunkType: 'page',
    pageNumber: index + 1,
    wordCount: chunk.trim().split(/\s+/).filter(word => word.length > 0).length,
    hasImages: false,
    imageCount: 0,
    hasStructuredItems: false
  }));

  console.log(`📊 Testing with ${testChunks.length} chunks:`);
  testChunks.forEach((chunk, index) => {
    const wordCount = chunk.trim().split(/\s+/).filter(word => word.length > 0).length;
    console.log(`   📄 Page ${index + 1}: ${chunk.length} chars, ${wordCount} words`);
  });

  // Apply filtering
  console.log('\n🧹 Applying chunk filtering...');
  const { filteredChunks, filteredMetadata, filterReport } = filterJunkChunks(testChunks, testMetadata);

  // Report results
  console.log(`\n📊 Filtering Results:`);
  console.log(`✅ High-quality chunks: ${filteredChunks.length}/${testChunks.length}`);
  console.log(`🗑️  Filtered junk chunks: ${filterReport.filtered.length}/${testChunks.length}`);
  console.log(`💰 Cost savings: ${Math.round((filterReport.filtered.length / testChunks.length) * 100)}% fewer embeddings`);

  console.log(`\n🗑️  Filtered chunks details:`);
  filterReport.filtered.forEach(item => {
    console.log(`   📄 ${item.identifier}: ${item.reason} (${item.chars} chars, ${item.words} words)`);
  });

  console.log(`\n✅ Kept chunks:`);
  filteredChunks.forEach((chunk, index) => {
    const metadata = filteredMetadata[index];
    console.log(`   📄 Page ${metadata.pageNumber}: ${chunk.length} chars, ${metadata.qualityScore.wordCount} words (Quality: ${metadata.qualityScore.meaningfulRatio}% meaningful)`);
  });

  console.log(`\n📈 Filter Reason Breakdown:`);
  Object.entries(filterReport.reasons).forEach(([reason, count]) => {
    if (count > 0) {
      console.log(`   ${reason}: ${count} chunks`);
    }
  });

  console.log('\n🎉 Chunk filtering test completed successfully!');
  console.log('This demonstrates how junk chunks are automatically identified and filtered out,');
  console.log('saving embedding costs and improving vector search quality.');
}

// Run test if this file is executed directly
if (require.main === module) {
  testChunkFiltering().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  filterJunkChunks,
  testChunkFiltering
};
