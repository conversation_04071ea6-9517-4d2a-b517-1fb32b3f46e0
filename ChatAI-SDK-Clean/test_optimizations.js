#!/usr/bin/env node

/**
 * Test script for embedding and Qdrant storage optimizations
 * Run with: node test_optimizations.js
 */

const embeddingService = require('./src/services/embeddingService');
const qdrantService = require('./src/services/qdrantService');

async function testEmbeddingOptimizations() {
  console.log('🧪 Testing Embedding Service Optimizations');
  console.log('==========================================\n');

  // Test data - simulate document chunks
  const testTexts = [
    'This is the first test chunk of text for embedding generation.',
    'Here is another chunk that contains different content for testing.',
    'The third chunk has some technical information about vector databases.',
    'Fourth chunk discusses machine learning and natural language processing.',
    'Fifth chunk contains information about document processing pipelines.',
    'Sixth chunk talks about optimization techniques for batch processing.',
    'Seventh chunk covers rate limiting and API management strategies.',
    'Eighth chunk discusses vector similarity search and retrieval.',
    'Ninth chunk contains metadata about document structure and pages.',
    'Tenth chunk concludes with performance metrics and monitoring.'
  ];

  try {
    console.log(`📊 Testing with ${testTexts.length} text chunks`);
    
    // Test sequential processing (original)
    console.log('\n🔄 Testing Sequential Processing (Original):');
    const startSequential = Date.now();
    const sequentialEmbeddings = await embeddingService.generateBatchEmbeddings(
      testTexts, null, 3, false // useParallel = false
    );
    const sequentialTime = Date.now() - startSequential;
    console.log(`⏱️ Sequential processing time: ${sequentialTime}ms`);

    // Test parallel processing (optimized)
    console.log('\n⚡ Testing Parallel Processing (Optimized):');
    const startParallel = Date.now();
    const parallelEmbeddings = await embeddingService.generateBatchEmbeddings(
      testTexts, null, 3, true // useParallel = true
    );
    const parallelTime = Date.now() - startParallel;
    console.log(`⏱️ Parallel processing time: ${parallelTime}ms`);

    // Compare results
    const speedup = sequentialTime / parallelTime;
    console.log(`\n📈 Performance Comparison:`);
    console.log(`🚀 Speedup: ${speedup.toFixed(2)}x faster`);
    console.log(`💾 Embeddings generated: ${parallelEmbeddings.filter(e => e !== null).length}/${testTexts.length}`);

    return { sequentialEmbeddings, parallelEmbeddings, testTexts };

  } catch (error) {
    console.error('❌ Embedding optimization test failed:', error.message);
    throw error;
  }
}

async function testQdrantOptimizations(testData) {
  console.log('\n🧪 Testing Qdrant Storage Optimizations');
  console.log('=======================================\n');

  const { parallelEmbeddings, testTexts } = testData;

  try {
    // Initialize Qdrant
    await qdrantService.initialize();
    console.log('✅ Qdrant service initialized');

    // Prepare test documents
    const documentsWithEmbeddings = testTexts.map((text, i) => ({
      document: {
        text: text,
        metadata: {
          appId: 'test-app',
          documentId: 'test-doc-123',
          filename: 'test-document.txt',
          userId: 'test-user',
          chunkIndex: i,
          totalChunks: testTexts.length,
          chunkType: 'test',
          pageNumber: Math.floor(i / 3) + 1
        }
      },
      embeddings: parallelEmbeddings[i]
    })).filter(item => item.embeddings !== null);

    console.log(`📦 Prepared ${documentsWithEmbeddings.length} documents for storage`);

    // Test individual storage (original)
    console.log('\n🔄 Testing Individual Storage (Original):');
    const startIndividual = Date.now();
    let individualStored = 0;
    for (const item of documentsWithEmbeddings.slice(0, 5)) { // Test with first 5 items
      try {
        await qdrantService.storeDocument(item.document, item.embeddings);
        individualStored++;
      } catch (error) {
        console.warn(`⚠️ Individual storage failed for chunk ${item.document.metadata.chunkIndex}`);
      }
    }
    const individualTime = Date.now() - startIndividual;
    console.log(`⏱️ Individual storage time: ${individualTime}ms for ${individualStored} chunks`);

    // Test batch storage (optimized)
    console.log('\n⚡ Testing Batch Storage (Optimized):');
    const startBatch = Date.now();
    const batchResult = await qdrantService.storeDocumentsBatch(
      documentsWithEmbeddings.slice(5), // Use remaining items
      3 // Small batch size for testing
    );
    const batchTime = Date.now() - startBatch;
    console.log(`⏱️ Batch storage time: ${batchTime}ms for ${batchResult.storedChunks.length} chunks`);

    // Compare results
    const avgIndividualTime = individualTime / individualStored;
    const avgBatchTime = batchTime / batchResult.storedChunks.length;
    const storageSpeedup = avgIndividualTime / avgBatchTime;

    console.log(`\n📈 Storage Performance Comparison:`);
    console.log(`🚀 Storage speedup: ${storageSpeedup.toFixed(2)}x faster per chunk`);
    console.log(`✅ Batch success rate: ${batchResult.successRate}%`);
    console.log(`📊 Total chunks stored: ${individualStored + batchResult.storedChunks.length}`);

    return batchResult;

  } catch (error) {
    console.error('❌ Qdrant optimization test failed:', error.message);
    throw error;
  }
}

async function runOptimizationTests() {
  console.log('🚀 Starting Optimization Tests');
  console.log('==============================\n');

  try {
    // Test embedding optimizations
    const embeddingTestData = await testEmbeddingOptimizations();

    // Test Qdrant storage optimizations
    const storageTestData = await testQdrantOptimizations(embeddingTestData);

    console.log('\n🎉 All Optimization Tests Completed Successfully!');
    console.log('================================================');
    console.log('✅ Embedding generation optimizations: WORKING');
    console.log('✅ Qdrant batch storage optimizations: WORKING');
    console.log('🚀 Performance improvements verified');

  } catch (error) {
    console.error('\n❌ Optimization tests failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runOptimizationTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testEmbeddingOptimizations,
  testQdrantOptimizations,
  runOptimizationTests
};
