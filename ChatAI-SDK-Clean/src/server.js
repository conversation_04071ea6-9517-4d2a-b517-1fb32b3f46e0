const express = require('express');
const cors = require('cors');
const config = require('./config');

const app = express();

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept'],
  credentials: true
}));

app.use(express.json({ limit: '25mb' }));
app.use(express.urlencoded({ extended: true, limit: '25mb' }));

// Routes
const routes = require('./routes');
app.use('/', routes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Unhandled error:', err);
  res.status(500).json({
    error: true,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: true,
    message: 'Endpoint not found'
  });
});

const PORT = config.port || 3002;

app.listen(PORT, () => {
  console.log(`🚀 ChatAI SDK Clean running on port ${PORT}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /api/v1/?apikey=...&query=...  - Main chat endpoint`);
  console.log(`   GET  /health                        - Health check`);
  console.log(`\n🔧 Configuration:`);
  console.log(`   User Service: ${config.userService.url}`);
  console.log(`   Qdrant Vector DB: ${config.qdrant.url}`);
  console.log(`   OpenRouter: ${config.openRouter.baseUrl}`);
});

module.exports = app;
