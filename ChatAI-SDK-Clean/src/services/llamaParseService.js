const fetch = require('node-fetch');
const FormData = require('form-data');

/**
 * LlamaParse Service for document parsing and text extraction
 * Moved from User-Service for better separation of concerns
 */
class LlamaParseService {
  constructor() {
    // Use the working API key
    this.apiKey = process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
    this.baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
    this.isConfigured = !!this.apiKey;

    if (!this.apiKey) {
      console.warn('⚠️  LLAMA_CLOUD_API_KEY not configured. Document parsing will be disabled.');
    } else {
      console.log('✅ LlamaParseService initialized successfully');
    }
  }

  checkConfiguration() {
    if (!this.isConfigured) {
      throw new Error('LlamaParse service is not configured. Please set LLAMA_CLOUD_API_KEY.');
    }
  }

  /**
   * Parse file using LlamaParse API
   */
  async parseFile(fileBuffer, filename) {
    this.checkConfiguration();

    try {
      console.log(`📄 Starting to parse file: ${filename}`);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', fileBuffer, {
        filename: filename,
        contentType: this.getContentType(filename)
      });

      // Upload file and get job ID
      const uploadResponse = await fetch(`${this.baseUrl}/parsing/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          ...formData.getHeaders()
        },
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error(`❌ LlamaParse upload error: ${uploadResponse.status} - ${errorText}`);
        throw new Error(`Upload failed: ${uploadResponse.status}`);
      }

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.id;

      if (!jobId) {
        throw new Error('Failed to get job ID from upload response');
      }

      console.log(`🔄 File uploaded, job ID: ${jobId}. Waiting for processing...`);

      // Poll for completion
      let attempts = 0;
      const maxAttempts = 60; // 5 minutes max (5 second intervals)

      while (attempts < maxAttempts) {
        try {
          const statusResponse = await this.getParseStatus(jobId);
          console.log(`📊 Job ${jobId} status check ${attempts + 1}: ${statusResponse.status}`);

          if (statusResponse.status === 'SUCCESS') {
            // Get the result in JSON format (includes pages array)
            const result = await this.getParseResult(jobId);
            console.log(`✅ Parsing completed for job ${jobId}`);

            // Process the JSON result which includes pages array
            if (!result.pages || !Array.isArray(result.pages)) {
              throw new Error('Invalid result format: missing pages array');
            }

            const pages = result.pages;
            const pageCount = pages.length;
            console.log(`📄 Document has ${pageCount} pages`);

            // Extract full text for backward compatibility
            const fullText = pages
              .map(page => page.md || page.text || '')
              .join('\n\n');

            if (!fullText.trim()) {
              throw new Error('No parsable text found in any page');
            }

            // Return both page-based and full text data
            return {
              id: jobId,
              status: 'SUCCESS',
              result: {
                // Full text for backward compatibility
                text: fullText,
                // Page-based data for improved chunking
                pages: pages.map((page, index) => ({
                  pageNumber: page.page || index + 1,
                  text: page.text || '',
                  markdown: page.md || '',
                  images: page.images || [],
                  items: page.items || [],
                  wordCount: this.countWords(page.text || page.md || '')
                })),
                metadata: {
                  page_count: pageCount,
                  word_count: this.countWords(fullText),
                  filename: filename,
                  job_metadata: result.job_metadata || {}
                },
              },
            };
          } else if (statusResponse.status === 'ERROR' || statusResponse.status === 'FAILED') {
            const errorMessage = statusResponse.error || statusResponse.message || 'Parsing failed';
            console.error(`❌ Job ${jobId} failed: ${errorMessage}`);
            throw new Error(`Parsing failed: ${errorMessage}`);
          }

          // Wait 5 seconds before checking again
          await new Promise(resolve => setTimeout(resolve, 5000));
          attempts++;
        } catch (error) {
          if (attempts >= maxAttempts - 1) {
            throw error;
          }
          attempts++;
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      }

      throw new Error('Parsing timeout - job took too long to complete');
    } catch (error) {
      console.error(`❌ LlamaParse error for ${filename}:`, error.message);
      throw error;
    }
  }

  /**
   * Get parsing job status
   */
  async getParseStatus(jobId) {
    const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Status check failed: ${response.status} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get parsing job result in JSON format (includes pages array)
   */
  async getParseResult(jobId) {
    const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}/result/json`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Result fetch failed: ${response.status} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Count words in text
   */
  countWords(text) {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Get content type based on file extension
   */
  getContentType(filename) {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      md: 'text/markdown',
      html: 'text/html',
      rtf: 'application/rtf',
    };

    return contentTypes[ext] || 'application/octet-stream';
  }

  /**
   * Parse text content directly (for already extracted text)
   */
  async parseTextContent(text, filename = 'text_content.txt') {
    console.log(`📄 Processing text content directly: ${filename}`);

    const wordCount = this.countWords(text);

    return {
      id: `text_${Date.now()}`,
      status: 'SUCCESS',
      result: {
        text: text,
        metadata: {
          page_count: 1,
          word_count: wordCount,
          filename: filename,
        },
      },
    };
  }
}

module.exports = new LlamaParseService();
