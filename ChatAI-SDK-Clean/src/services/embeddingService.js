const fetch = require('node-fetch');
const config = require('../config');

class EmbeddingService {
  constructor() {
    this.openaiApiKey = config.openai?.apiKey || process.env.OPENAI_API_KEY;
    this.sambaNovaApiKey = process.env.SAMBANOVA_API_KEY || '4371d613-5ad6-4ec6-b92f-8fe941fd8142';

    // Force SambaNova for testing - no fallbacks
    this.provider = 'sambanova'; // Always use SambaNova for focus testing

    // Original priority logic - COMMENTED OUT
    // this.provider = this.sambaNovaApiKey ? 'sambanova' :
    //                this.openaiApiKey ? 'openai' : 'mock';

    // Model configurations
    this.models = {
      sambanova: {
        model: 'E5-Mistral-7B-Instruct',
        dimensions: 4096, // E5-Mistral-7B-Instruct embedding dimensions
        baseUrl: 'https://api.sambanova.ai/v1/embeddings'
      },
      openai: {
        model: 'text-embedding-ada-002',
        dimensions: 1536,
        baseUrl: 'https://api.openai.com/v1/embeddings'
      }
    };

    // Rate limiting tracker
    this.rateLimitTracker = {
      sambanova: {
        requestsToday: 0,
        requestsThisHour: 0,
        requestsThisMinute: 0,
        lastResetDay: new Date().getDate(),
        lastResetHour: new Date().getHours(),
        lastResetMinute: new Date().getMinutes()
      },
      openai: {
        requestsToday: 0,
        requestsThisHour: 0,
        requestsThisMinute: 0,
        lastResetDay: new Date().getDate(),
        lastResetHour: new Date().getHours(),
        lastResetMinute: new Date().getMinutes()
      }
    };

    this.currentModel = this.models[this.provider] || this.models.openai;
    // Force SambaNova configuration check
    this.isConfigured = !!this.sambaNovaApiKey;

    console.log('🔧 SambaNova Embedding Service Configuration (Focus Mode):');
    console.log(`   Provider: ${this.provider.toUpperCase()} (FORCED)`);
    console.log(`   Model: ${this.currentModel.model}`);
    console.log(`   Dimensions: ${this.currentModel.dimensions}`);
    console.log(`   SambaNova API Key: ${this.sambaNovaApiKey ? '✅ Available' : '❌ REQUIRED'}`);
    console.log(`   Fallbacks: 🚫 DISABLED for focus testing`);

    if (!this.isConfigured) {
      console.error('❌ SambaNova API key required for focus testing!');
      console.error('💡 Add SAMBANOVA_API_KEY to environment variables');
    } else {
      console.log(`✅ ${this.provider.toUpperCase()} embedding service initialized (FOCUS MODE)`);
    }
  }

  /**
   * Generate embeddings for text using OpenAI API
   * @param {string} text - Text to generate embeddings for
   * @returns {Promise<number[]>} Array of embedding values
   */
  async generateEmbeddings(text) {
    try {
      console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔍 GENERATING VECTOR EMBEDDINGS FOR USER QUERY`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`📝 Query Text: "${text}"`);
      console.log(`📏 Text Length: ${text.length} characters`);
      console.log(`🤖 Provider: ${this.provider.toUpperCase()}`);
      console.log(`🤖 Model: ${this.currentModel.model}`);
      console.log(`📊 Expected Dimensions: ${this.currentModel.dimensions}`);

      if (!this.isConfigured) {
        console.error(`❌ SambaNova API key required for focus testing!`);
        console.error(`🚫 Mock embeddings disabled in focus mode`);
        console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
        throw new Error('SambaNova API key required - no fallbacks in focus mode');
      }

      // Focus on SambaNova only - no fallbacks for testing
      if (this.provider === 'sambanova') {
        console.log(`🎯 Using SambaNova embeddings (no fallbacks)`);
        return await this.generateSambaNovaEmbeddings(text);
      } else if (this.provider === 'openai') {
        console.log(`🎯 Using OpenAI embeddings (no fallbacks)`);
        return await this.generateOpenAIEmbeddings(text);
      } else {
        console.log(`🎯 Using mock embeddings (no API keys)`);
        const mockEmbeddings = this.generateMockEmbeddings(text);
        console.log(`✅ Generated mock embeddings: ${mockEmbeddings.length} dimensions`);
        console.log(`📊 Sample values: [${mockEmbeddings.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
        console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
        return mockEmbeddings;
      }

      // Fallback methods commented out for SambaNova focus
      /*
      // Try primary provider first
      if (this.provider === 'sambanova') {
        try {
          return await this.generateSambaNovaEmbeddings(text);
        } catch (sambaError) {
          console.warn(`⚠️ SambaNova failed: ${sambaError.message}`);
          if (this.openaiApiKey) {
            console.log(`🔄 Falling back to OpenAI...`);
            return await this.generateOpenAIEmbeddings(text);
          }
          throw sambaError;
        }
      } else if (this.provider === 'openai') {
        try {
          return await this.generateOpenAIEmbeddings(text);
        } catch (openaiError) {
          console.warn(`⚠️ OpenAI failed: ${openaiError.message}`);
          throw openaiError;
        }
      }
      */

    } catch (error) {
      console.error('❌ Failed to generate embeddings:', error.message);
      console.error('🚫 No fallbacks enabled - SambaNova focus mode');
      console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

      // Throw error instead of falling back to mock embeddings
      throw new Error(`SambaNova embedding failed: ${error.message}`);

      // Fallback to mock embeddings - COMMENTED OUT for SambaNova focus
      /*
      console.log('🔄 Falling back to mock embeddings');
      const mockEmbeddings = this.generateMockEmbeddings(text);
      console.log(`✅ Generated fallback mock embeddings: ${mockEmbeddings.length} dimensions`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
      return mockEmbeddings;
      */
    }
  }

  /**
   * Generate embeddings using SambaNova API (single text)
   */
  async generateSambaNovaEmbeddings(text) {
    console.log(`🌐 Calling SambaNova Embeddings API (single)...`);
    const startTime = Date.now();

    const response = await fetch(this.models.sambanova.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.sambaNovaApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.models.sambanova.model,
        input: text,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ SambaNova API error: ${response.status} - ${errorText}`);
      throw new Error(`SambaNova API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const embeddings = data.data[0].embedding;
    const duration = Date.now() - startTime;

    console.log(`✅ Generated SambaNova embeddings in ${duration}ms`);
    console.log(`📊 Embedding dimensions: ${embeddings.length}`);
    console.log(`📊 Sample values: [${embeddings.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
    console.log(`💰 Usage: ${data.usage?.total_tokens || 'unknown'} tokens`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return embeddings;
  }

  /**
   * Generate embeddings using SambaNova API (batch processing)
   * @param {string[]} texts - Array of texts to embed
   * @returns {Promise<number[][]>} Array of embedding vectors
   */
  async generateSambaNovaBatchEmbeddings(texts) {
    console.log(`🚀 Calling SambaNova Batch Embeddings API for ${texts.length} texts...`);

    // Check rate limits before making request
    this.checkRateLimits('sambanova');

    const startTime = Date.now();

    const response = await fetch(this.models.sambanova.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.sambaNovaApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.models.sambanova.model,
        input: texts, // Send array of texts
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ SambaNova Batch API error: ${response.status} - ${errorText}`);

      // Check if it's a rate limit error
      if (response.status === 429) {
        throw new Error(`Rate limit exceeded: ${errorText}`);
      }

      throw new Error(`SambaNova Batch API error: ${response.status} ${response.statusText}`);
    }

    // Record successful request
    this.recordRequest('sambanova');

    const data = await response.json();
    const embeddings = data.data.map(item => item.embedding);
    const duration = Date.now() - startTime;

    console.log(`✅ Generated ${embeddings.length} SambaNova embeddings in ${duration}ms`);
    console.log(`📊 Embedding dimensions: ${embeddings[0].length}`);
    console.log(`📊 Average time per embedding: ${(duration / embeddings.length).toFixed(2)}ms`);
    console.log(`💰 Total usage: ${data.usage?.total_tokens || 'unknown'} tokens`);
    console.log(`🚀 Batch processing speedup: ~${Math.round(embeddings.length * 1000 / duration)}x faster`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return embeddings;
  }

  /**
   * Generate embeddings using OpenAI API (single text)
   */
  async generateOpenAIEmbeddings(text) {
    console.log(`🌐 Calling OpenAI Embeddings API (single)...`);
    const startTime = Date.now();

    const response = await fetch(this.models.openai.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.models.openai.model,
        input: text,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ OpenAI API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const embeddings = data.data[0].embedding;
    const duration = Date.now() - startTime;

    console.log(`✅ Generated OpenAI embeddings in ${duration}ms`);
    console.log(`📊 Embedding dimensions: ${embeddings.length}`);
    console.log(`📊 Sample values: [${embeddings.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
    console.log(`💰 Usage: ${data.usage?.total_tokens || 'unknown'} tokens`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return embeddings;
  }

  /**
   * Generate embeddings using OpenAI API (batch processing)
   * @param {string[]} texts - Array of texts to embed
   * @returns {Promise<number[][]>} Array of embedding vectors
   */
  async generateOpenAIBatchEmbeddings(texts) {
    console.log(`🚀 Calling OpenAI Batch Embeddings API for ${texts.length} texts...`);
    const startTime = Date.now();

    const response = await fetch(this.models.openai.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.models.openai.model,
        input: texts, // Send array of texts
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ OpenAI Batch API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenAI Batch API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const embeddings = data.data.map(item => item.embedding);
    const duration = Date.now() - startTime;

    console.log(`✅ Generated ${embeddings.length} OpenAI embeddings in ${duration}ms`);
    console.log(`📊 Embedding dimensions: ${embeddings[0].length}`);
    console.log(`📊 Average time per embedding: ${(duration / embeddings.length).toFixed(2)}ms`);
    console.log(`💰 Total usage: ${data.usage?.total_tokens || 'unknown'} tokens`);
    console.log(`🚀 Batch processing speedup: ~${Math.round(embeddings.length * 1000 / duration)}x faster`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return embeddings;
  }

  /**
   * Generate mock embeddings for development/testing
   * This creates a simple hash-based embedding vector that's consistent
   * @param {string} text - Text to generate mock embeddings for
   * @returns {number[]} Array of mock embedding values
   */
  generateMockEmbeddings(text) {
    const dimension = this.currentModel.dimensions; // Match current provider dimensions
    const embeddings = new Array(dimension).fill(0);

    // Simple hash-based approach for consistent mock embeddings
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Generate pseudo-random but deterministic values
    for (let i = 0; i < dimension; i++) {
      const seed = hash + i;
      const x = Math.sin(seed) * 10000;
      embeddings[i] = (x - Math.floor(x)) * 2 - 1; // Normalize to [-1, 1]
    }

    // Normalize the vector to unit length
    const magnitude = Math.sqrt(embeddings.reduce((sum, val) => sum + val * val, 0));
    for (let i = 0; i < dimension; i++) {
      embeddings[i] = embeddings[i] / magnitude;
    }

    return embeddings;
  }

  /**
   * Calculate cosine similarity between two embedding vectors
   * @param {number[]} a - First embedding vector
   * @param {number[]} b - Second embedding vector
   * @returns {number} Cosine similarity score (0-1)
   */
  calculateCosineSimilarity(a, b) {
    if (a.length !== b.length) {
      throw new Error('Embedding vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    const similarity = dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    return Math.max(0, Math.min(1, similarity)); // Clamp to [0, 1]
  }

  /**
   * Check if API key is available
   */
  hasApiKey() {
    return this.isConfigured;
  }

  /**
   * Generate embeddings for multiple texts (batch processing with robust retry)
   * @param {string[]} texts - Array of texts to embed
   * @param {number} batchSize - Maximum number of texts per API call
   * @param {number} maxRetries - Maximum retries per batch (default: 3)
   * @returns {Promise<number[][]>} Array of embedding vectors
   */
  async generateBatchEmbeddings(texts, batchSize = null, maxRetries = 3) {
    // Rate limit configurations per provider
    const rateLimits = {
      sambanova: {
        rpm: 30,    // 30 requests per minute
        rph: 150,   // 150 requests per hour
        rpd: 1800,  // 1800 requests per day
        batchSize: 20,  // Optimal batch size
        delayBetweenBatches: 2100 // 2.1 seconds (30 RPM = 1 request per 2 seconds + buffer)
      },
      openai: {
        rpm: 3000,  // Much higher limits
        rph: 180000,
        rpd: 4320000,
        batchSize: 50,  // Can handle larger batches
        delayBetweenBatches: 100 // Minimal delay
      }
    };

    const currentLimits = rateLimits[this.provider] || rateLimits.openai;
    const effectiveBatchSize = batchSize || currentLimits.batchSize;

    console.log(`🚀 Starting batch embedding generation for ${texts.length} texts`);
    console.log(`📦 Provider: ${this.provider}`);
    console.log(`📦 Batch size: ${effectiveBatchSize} texts per API call`);
    console.log(`🔄 Max retries per batch: ${maxRetries}`);
    console.log(`⏱️ Rate limits: ${currentLimits.rpm} RPM, ${currentLimits.rph} RPH, ${currentLimits.rpd} RPD`);
    console.log(`⏳ Delay between batches: ${currentLimits.delayBetweenBatches}ms`);

    const allEmbeddings = [];
    const totalBatches = Math.ceil(texts.length / effectiveBatchSize);
    const estimatedTime = (totalBatches * currentLimits.delayBetweenBatches) / 1000;
    const failedBatches = [];
    let successfulBatches = 0;

    console.log(`📊 Total batches: ${totalBatches}`);
    console.log(`⏰ Estimated processing time: ${Math.ceil(estimatedTime / 60)} minutes`);

    for (let i = 0; i < texts.length; i += effectiveBatchSize) {
      const batch = texts.slice(i, i + effectiveBatchSize);
      const batchNumber = Math.floor(i / effectiveBatchSize) + 1;
      const batchStartIndex = i;

      console.log(`\n📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} texts)`);

      let batchSuccess = false;
      let retryCount = 0;

      while (!batchSuccess && retryCount <= maxRetries) {
        try {
          if (retryCount > 0) {
            console.log(`🔄 Retry attempt ${retryCount}/${maxRetries} for batch ${batchNumber}`);
          }

          let batchEmbeddings;

          if (this.provider === 'sambanova') {
            batchEmbeddings = await this.generateSambaNovaBatchEmbeddings(batch);
          } else if (this.provider === 'openai') {
            batchEmbeddings = await this.generateOpenAIBatchEmbeddings(batch);
          } else {
            // For mock provider, generate embeddings one by one
            batchEmbeddings = batch.map(text => this.generateMockEmbeddings(text));
          }

          // Validate embeddings
          if (!batchEmbeddings || batchEmbeddings.length !== batch.length) {
            throw new Error(`Invalid embeddings: expected ${batch.length}, got ${batchEmbeddings?.length || 0}`);
          }

          // Store embeddings in correct positions
          for (let j = 0; j < batchEmbeddings.length; j++) {
            allEmbeddings[batchStartIndex + j] = batchEmbeddings[j];
          }

          batchSuccess = true;
          successfulBatches++;
          console.log(`✅ Batch ${batchNumber}/${totalBatches} completed successfully`);
          console.log(`📊 Progress: ${successfulBatches}/${totalBatches} batches (${Math.round(successfulBatches / totalBatches * 100)}%)`);

        } catch (error) {
          retryCount++;
          console.error(`❌ Batch ${batchNumber} attempt ${retryCount} failed:`, error.message);

          if (retryCount <= maxRetries) {
            // Determine retry delay based on error type
            let retryDelay = 1000; // Default 1 second

            if (error.message.includes('rate limit') || error.message.includes('429')) {
              retryDelay = 60000; // 60 seconds for rate limit
              console.log(`🚦 Rate limit error detected, waiting ${retryDelay / 1000} seconds...`);
            } else if (error.message.includes('timeout') || error.message.includes('network')) {
              retryDelay = 5000; // 5 seconds for network issues
              console.log(`🌐 Network error detected, waiting ${retryDelay / 1000} seconds...`);
            } else if (error.message.includes('server') || error.message.includes('500')) {
              retryDelay = 10000; // 10 seconds for server errors
              console.log(`🖥️ Server error detected, waiting ${retryDelay / 1000} seconds...`);
            } else {
              console.log(`🔄 Unknown error, waiting ${retryDelay / 1000} seconds...`);
            }

            await new Promise(resolve => setTimeout(resolve, retryDelay));
          } else {
            // Max retries exceeded
            console.error(`💥 Batch ${batchNumber} failed after ${maxRetries} retries`);
            failedBatches.push({
              batchNumber,
              batchStartIndex,
              batch,
              error: error.message
            });

            // Fill with null embeddings to maintain array structure
            for (let j = 0; j < batch.length; j++) {
              allEmbeddings[batchStartIndex + j] = null;
            }

            batchSuccess = true; // Exit retry loop
          }
        }
      }

      // Rate limiting delay between batches (only if not the last batch)
      if (batchNumber < totalBatches && batchSuccess) {
        console.log(`⏳ Rate limiting: waiting ${currentLimits.delayBetweenBatches}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, currentLimits.delayBetweenBatches));
      }
    }

    // Summary report
    console.log(`\n🎉 Batch embedding generation completed!`);
    console.log(`📊 Total embeddings requested: ${texts.length}`);
    console.log(`✅ Successful batches: ${successfulBatches}/${totalBatches}`);
    console.log(`❌ Failed batches: ${failedBatches.length}/${totalBatches}`);

    if (failedBatches.length > 0) {
      console.log(`\n💥 Failed batch details:`);
      failedBatches.forEach(failed => {
        console.log(`   Batch ${failed.batchNumber}: ${failed.error}`);
      });

      // Filter out null embeddings for return
      const validEmbeddings = allEmbeddings.filter(emb => emb !== null);
      console.log(`📊 Valid embeddings generated: ${validEmbeddings.length}/${texts.length}`);

      if (validEmbeddings.length === 0) {
        throw new Error(`All batches failed. No embeddings generated.`);
      }

      // Return partial results with warning
      console.log(`⚠️ Returning partial results. Some embeddings may be missing.`);
      return allEmbeddings; // Includes nulls to maintain index alignment
    }

    console.log(`📊 Embedding dimensions: ${allEmbeddings[0]?.length || 'unknown'}`);
    return allEmbeddings;
  }

  /**
   * Check and update rate limit tracking
   */
  checkRateLimits(provider) {
    const now = new Date();
    const tracker = this.rateLimitTracker[provider];

    // Reset counters if time periods have passed
    if (now.getDate() !== tracker.lastResetDay) {
      tracker.requestsToday = 0;
      tracker.lastResetDay = now.getDate();
    }

    if (now.getHours() !== tracker.lastResetHour) {
      tracker.requestsThisHour = 0;
      tracker.lastResetHour = now.getHours();
    }

    if (now.getMinutes() !== tracker.lastResetMinute) {
      tracker.requestsThisMinute = 0;
      tracker.lastResetMinute = now.getMinutes();
    }

    // Rate limits per provider
    const limits = {
      sambanova: { rpm: 30, rph: 150, rpd: 1800 },
      openai: { rpm: 3000, rph: 180000, rpd: 4320000 }
    };

    const currentLimits = limits[provider] || limits.openai;

    // Check if we're approaching limits
    const usage = {
      minute: { current: tracker.requestsThisMinute, limit: currentLimits.rpm },
      hour: { current: tracker.requestsThisHour, limit: currentLimits.rph },
      day: { current: tracker.requestsToday, limit: currentLimits.rpd }
    };

    // Log current usage
    console.log(`📊 Rate limit usage for ${provider}:`);
    console.log(`   Minute: ${usage.minute.current}/${usage.minute.limit} (${Math.round(usage.minute.current / usage.minute.limit * 100)}%)`);
    console.log(`   Hour: ${usage.hour.current}/${usage.hour.limit} (${Math.round(usage.hour.current / usage.hour.limit * 100)}%)`);
    console.log(`   Day: ${usage.day.current}/${usage.day.limit} (${Math.round(usage.day.current / usage.day.limit * 100)}%)`);

    // Check if we can make another request
    const canMakeRequest =
      tracker.requestsThisMinute < currentLimits.rpm &&
      tracker.requestsThisHour < currentLimits.rph &&
      tracker.requestsToday < currentLimits.rpd;

    if (!canMakeRequest) {
      const blockedBy = [];
      if (tracker.requestsThisMinute >= currentLimits.rpm) blockedBy.push('minute limit');
      if (tracker.requestsThisHour >= currentLimits.rph) blockedBy.push('hour limit');
      if (tracker.requestsToday >= currentLimits.rpd) blockedBy.push('day limit');

      throw new Error(`Rate limit exceeded: ${blockedBy.join(', ')}`);
    }

    return { canMakeRequest, usage };
  }

  /**
   * Record a successful API request
   */
  recordRequest(provider) {
    const tracker = this.rateLimitTracker[provider];
    tracker.requestsThisMinute++;
    tracker.requestsThisHour++;
    tracker.requestsToday++;
  }

  /**
   * Get current provider information
   */
  getProviderInfo() {
    return {
      provider: this.provider,
      model: this.currentModel.model,
      dimensions: this.currentModel.dimensions,
      isConfigured: this.isConfigured,
      rateLimitUsage: this.rateLimitTracker[this.provider]
    };
  }

  /**
   * Split text into chunks for processing
   * @param {string} text - Text to split
   * @param {number} chunkSize - Size of each chunk
   * @param {number} overlap - Overlap between chunks
   * @returns {string[]} Array of text chunks
   */
  splitTextIntoChunks(text, chunkSize = 1000, overlap = 100) {
    const chunks = [];
    let start = 0;

    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      const chunk = text.slice(start, end);
      chunks.push(chunk);

      if (end === text.length) break;
      start = end - overlap;
    }

    return chunks;
  }
}

module.exports = new EmbeddingService();
