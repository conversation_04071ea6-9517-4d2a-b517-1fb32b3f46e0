<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR policies chatBot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .user .message-avatar {
            background: #667eea;
            color: white;
        }

        .bot .message-avatar {
            background: #28a745;
            color: white;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-form {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 24px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.2s;
            max-height: 120px;
            min-height: 48px;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        #sendButton {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        #sendButton:hover:not(:disabled) {
            background: #5a6fd8;
            transform: scale(1.05);
        }

        #sendButton:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 8px 16px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        /* Scrollbar styling */
        .messages-container::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .chat-container {
                height: calc(100vh - 20px);
                border-radius: 12px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 ChatAI Streaming Interface</h1>
            <p>Real-time AI responses with document context</p>
        </div>

        <div class="messages-container" id="messagesContainer">
            <div class="message bot">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    Hello! I'm your AI assistant with access to your documents. Ask me anything and I'll provide real-time streaming responses.
                </div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-form">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="Type your message here..." 
                        rows="1"
                    ></textarea>
                </div>
                <button id="sendButton" type="button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="status" id="status">Ready to chat • Connected to ChatAI API</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1/';
        const API_KEY = 'test_api_key_1752470355743_kshejjgq3';
        
        let isStreaming = false;
        let currentStreamingMessage = null;

        // DOM elements
        const messagesContainer = document.getElementById('messagesContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new lines)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Send button click
        sendButton.addEventListener('click', sendMessage);

        function updateStatus(message, type = 'normal') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addMessage(content, type = 'bot', isStreaming = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'user' ? '👤' : '🤖';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
            
            if (isStreaming) {
                currentStreamingMessage = contentDiv;
            }
            
            return contentDiv;
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message bot';
            typingDiv.id = 'typing-indicator';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';
            
            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.style.display = 'flex';
            indicator.innerHTML = `
                <span>AI is thinking</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;
            
            typingDiv.appendChild(avatar);
            typingDiv.appendChild(indicator);
            messagesContainer.appendChild(typingDiv);
            scrollToBottom();
            
            return typingDiv;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        function scrollToBottom() {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function setInputState(disabled) {
            messageInput.disabled = disabled;
            sendButton.disabled = disabled;
            isStreaming = disabled;
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isStreaming) return;

            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Set loading state
            setInputState(true);
            updateStatus('Sending message...', 'normal');
            
            const typingIndicator = showTypingIndicator();

            try {
                // Start streaming request
                const url = `${API_BASE}?apikey=${encodeURIComponent(API_KEY)}&query=${encodeURIComponent(message)}&stream=true`;
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                hideTypingIndicator();
                
                // Create initial bot message for streaming
                const botMessageContent = addMessage('', 'bot', true);
                updateStatus('Receiving response...', 'normal');

                // Handle streaming response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // Keep incomplete line in buffer
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.type === 'content') {
                                    botMessageContent.textContent += data.content;
                                    scrollToBottom();
                                } else if (data.type === 'done') {
                                    updateStatus('Response complete', 'success');
                                    setTimeout(() => {
                                        updateStatus('Ready to chat • Connected to ChatAI API', 'normal');
                                    }, 2000);
                                }
                            } catch (e) {
                                console.warn('Failed to parse SSE data:', line);
                            }
                        }
                    }
                }

            } catch (error) {
                hideTypingIndicator();
                console.error('Streaming error:', error);
                addMessage(`❌ Error: ${error.message}`, 'bot');
                updateStatus('Error occurred. Ready to try again.', 'error');
            } finally {
                setInputState(false);
                currentStreamingMessage = null;
                messageInput.focus();
            }
        }

        // Focus input on load
        window.addEventListener('load', () => {
            messageInput.focus();
        });
    </script>
</body>
</html>
