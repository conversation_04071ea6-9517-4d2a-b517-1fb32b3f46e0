import { Injectable, Logger } from '@nestjs/common';
import { QdrantClient } from '@qdrant/js-client-rest';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';

export interface VectorDocument {
  text: string;
  metadata: {
    appId: string; // Primary tenant isolation
    documentId: string; // Document tracking
    filename: string; // File reference
    userId: string; // Platform user tracking
    pageCount?: number; // Additional metadata
    wordCount?: number; // Additional metadata
  };
}

export interface VectorSearchResult {
  id: string;
  score: number;
  payload: VectorDocument['metadata'];
  text: string;
}

@Injectable()
export class VectorService {
  private readonly logger = new Logger(VectorService.name);
  private qdrantClient: QdrantClient;
  private readonly collectionName = 'chatai_documents';

  constructor(private configService: ConfigService) {
    this.initializeQdrant();
  }

  private async initializeQdrant() {
    try {
      const qdrantUrl = this.configService.get<string>(
        'QDRANT_URL',
        'http://localhost:6333',
      );

      this.qdrantClient = new QdrantClient({
        url: qdrantUrl,
      });

      // Test connection
      await this.qdrantClient.getCollections();
      this.logger.log('✅ Qdrant connection established successfully');

      // Ensure collection exists
      await this.ensureCollection();
    } catch (error) {
      this.logger.error('❌ Failed to initialize Qdrant:', error.message);
      throw error;
    }
  }

  private async ensureCollection() {
    try {
      // Check if collection exists
      const collections = await this.qdrantClient.getCollections();
      const collectionExists = collections.collections.some(
        (col) => col.name === this.collectionName,
      );

      if (!collectionExists) {
        this.logger.log(`Creating collection: ${this.collectionName}`);

        await this.qdrantClient.createCollection(this.collectionName, {
          vectors: {
            size: 384, // Updated for better mock embeddings (can be changed to 1536 when OpenAI key is added)
            distance: 'Cosine',
          },
          optimizers_config: {
            default_segment_number: 2,
          },
          replication_factor: 1,
        });

        this.logger.log(
          `✅ Collection ${this.collectionName} created successfully`,
        );
      } else {
        this.logger.log(`✅ Collection ${this.collectionName} already exists`);
      }
    } catch (error) {
      this.logger.error('❌ Failed to ensure collection:', error.message);
      throw error;
    }
  }

  /**
   * Store document text with embeddings in vector database
   */
  async storeDocument(
    document: VectorDocument,
    embeddings: number[],
  ): Promise<string> {
    try {
      // Generate a simple UUID for the point ID
      const pointId = uuidv4();

      // Validate embeddings
      if (!Array.isArray(embeddings) || embeddings.length === 0) {
        throw new Error('Invalid embeddings: must be a non-empty array');
      }

      // Validate embeddings are numbers
      if (!embeddings.every((val) => typeof val === 'number' && !isNaN(val))) {
        throw new Error('Invalid embeddings: all values must be valid numbers');
      }

      this.logger.log(
        `🔍 Storing document chunk with ${embeddings.length} dimensions`,
      );

      await this.qdrantClient.upsert(this.collectionName, {
        wait: true,
        points: [
          {
            id: pointId,
            vector: embeddings,
            payload: {
              text: document.text,
              ...document.metadata,
            },
          },
        ],
      });

      this.logger.log(`✅ Document stored with ID: ${pointId}`);
      return pointId;
    } catch (error) {
      this.logger.error('❌ Failed to store document:', error.message);
      this.logger.error('❌ Error details:', error);
      throw error;
    }
  }

  /**
   * Search for similar documents within a specific app (tenant isolation)
   */
  async searchDocuments(
    appId: string,
    queryEmbeddings: number[],
    limit: number = 5,
    scoreThreshold: number = 0.7,
  ): Promise<VectorSearchResult[]> {
    try {
      const searchResult = await this.qdrantClient.search(this.collectionName, {
        vector: queryEmbeddings,
        limit,
        score_threshold: scoreThreshold,
        filter: {
          must: [
            {
              key: 'appId',
              match: {
                value: appId,
              },
            },
          ],
        },
        with_payload: true,
      });

      const results: VectorSearchResult[] = searchResult.map((point) => ({
        id: point.id.toString(),
        score: point.score,
        payload: {
          appId: point.payload.appId as string,
          documentId: point.payload.documentId as string,
          filename: point.payload.filename as string,
          userId: point.payload.userId as string,
          pageCount: point.payload.pageCount as number,
          wordCount: point.payload.wordCount as number,
        },
        text: point.payload.text as string,
      }));

      this.logger.log(
        `🔍 Found ${results.length} similar documents for appId: ${appId}`,
      );
      return results;
    } catch (error) {
      this.logger.error('❌ Failed to search documents:', error.message);
      throw error;
    }
  }

  /**
   * Delete all documents for a specific app
   */
  async deleteAppDocuments(appId: string): Promise<void> {
    try {
      await this.qdrantClient.delete(this.collectionName, {
        filter: {
          must: [
            {
              key: 'appId',
              match: {
                value: appId,
              },
            },
          ],
        },
      });

      this.logger.log(`🗑️ Deleted all documents for appId: ${appId}`);
    } catch (error) {
      this.logger.error('❌ Failed to delete app documents:', error.message);
      throw error;
    }
  }

  /**
   * Delete a specific document with app isolation for security
   */
  async deleteDocument(documentId: string, appId?: string): Promise<number> {
    try {
      const filter: any = {
        must: [
          {
            key: 'documentId',
            match: {
              value: documentId,
            },
          },
        ],
      };

      // Add app isolation if appId is provided (recommended for security)
      if (appId) {
        filter.must.push({
          key: 'appId',
          match: {
            value: appId,
          },
        });
      }

      const deleteResult = await this.qdrantClient.delete(this.collectionName, {
        filter,
      });

      this.logger.log(
        `🗑️ Deleted document: ${documentId}${appId ? ` for app: ${appId}` : ''}`,
      );

      // Return operation ID as count estimate (Qdrant doesn't return exact count)
      return deleteResult.operation_id ? 1 : 0;
    } catch (error) {
      this.logger.error('❌ Failed to delete document:', error.message);
      throw error;
    }
  }

  /**
   * Get collection info and stats
   */
  async getCollectionInfo() {
    try {
      const info = await this.qdrantClient.getCollection(this.collectionName);
      return info;
    } catch (error) {
      this.logger.error('❌ Failed to get collection info:', error.message);
      throw error;
    }
  }
}
